
export default class IziApi {

    public static secure = true;

    public static api = "https://api.izi.co.tz/api";

    public static domain ="https://api.izi.co.tz/api";
    // public static domain = "127.0.0.1:8000";
    public static baseUrl = this.secure ? `https://${this.domain}` : `http://${this.domain}`;
    public static getImagesUrl = (path: string) => `${this.baseUrl}${path}`;

    // Auth endpoints
    public static login = `${this.api}/auth/login`;
    public static register = `${this.api}/auth/register`;
    public static loggedInUser = `${this.api}/auth/user`;

    // organizations endpoints
    public static organizations = `${this.api}/organizations`;
    
    public static getOrganizations = `${this.api}/config/organizations`;
    public static singleOrganization = (id: number) => `${this.api}/config/organizations/${id}`;


    // customers endpoints
    public static customers = `${this.api}/customer/customers`;

    // users endpoints
    public static users = `${this.api}/config/users`;

    //sales endpoints
    public static sales = `${this.api}/sale/sales`;

    //inventories endpoints
    public static inventories = `${this.api}/inventory/inventoryItems`;

    //inventory categories endpoints
    public static inventoryCategories = `${this.api}/inventory/inventoryCategories`;
    
    //purchases endpoints
    public static purchases = `${this.api}/purchase/purchases`;

    //expenses endpoints
    public static expenses = `${this.api}/expenses`;

    //expense categories endpoints
    public static expenseCategory = `${this.api}/expense-categories`;

    // suppliers endpoints
    public static suppliers = `${this.api}/supplies`;

    //credits endpoints
    public static credits = `${this.api}/credits`;
    
}