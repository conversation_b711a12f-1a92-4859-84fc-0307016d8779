import axios from "axios";
import <PERSON><PERSON><PERSON><PERSON> from "@/lib/endpoints";
import { BuyTokensRequest, BuyTokensResponse } from "../types/tokens";

export class Tokens {
    private static token = typeof window !== "undefined" ? localStorage.getItem("izi_token") : "";

    public static async buy(form: BuyTokensRequest, id: string): Promise<BuyTokensResponse> {
        try {
            const response = await axios.post(IziApi.organizations + `/${id}/add-tokens`, form, {
                headers: {
                    Authorization: `Bearer ${this.token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (response.status === 200 || response.status === 201) {
                return response.data;
            }
            throw new Error('Failed to buy tokens');
        } catch (error: unknown) {
            if (axios.isAxiosError(error) && error.response) {
                throw error.response.data;
            }
            throw error;
        }
    }

}
